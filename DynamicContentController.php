<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class DynamicContentController extends Controller
{
    /**
     * Store dynamic sections and generate scrollable HTML
     */
    public function store(Request $request)
    {
        // Validate the request
        $request->validate([
            'sections' => 'required|array|min:1',
            'sections.*.title' => 'required|string|max:255',
            'sections.*.content' => 'required|string',
            'document_title' => 'nullable|string|max:255'
        ]);

        try {
            DB::beginTransaction();

            $sections = $request->input('sections', []);
            $documentTitle = $request->input('document_title', 'Dynamic Content Document');

            // Create main document record
            $documentId = DB::table('dynamic_documents')->insertGetId([
                'title' => $documentTitle,
                'created_at' => now(),
                'updated_at' => now()
            ]);

            $sectionData = [];

            // Process each section
            foreach ($sections as $index => $section) {
                $title = trim($section['title']);
                $content = $section['content'];
                $slug = Str::slug($title) . '-' . ($index + 1);

                // Save section to database
                $sectionId = DB::table('dynamic_sections')->insertGetId([
                    'document_id' => $documentId,
                    'title' => $title,
                    'content' => $content,
                    'slug' => $slug,
                    'order_index' => $index,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);

                $sectionData[] = [
                    'id' => $sectionId,
                    'title' => $title,
                    'content' => $content,
                    'slug' => $slug,
                    'order' => $index
                ];
            }

            // Generate HTML content
            $htmlContent = $this->generateScrollableHtml($documentTitle, $sectionData);

            // Save generated HTML
            DB::table('dynamic_documents')
                ->where('id', $documentId)
                ->update([
                    'generated_html' => $htmlContent,
                    'updated_at' => now()
                ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Content saved successfully!',
                'document_id' => $documentId,
                'preview_url' => route('dynamic-content.preview', $documentId),
                'sections_count' => count($sectionData)
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Error saving content: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate scrollable HTML with linkable titles
     */
    private function generateScrollableHtml($documentTitle, $sections)
    {
        $tableOfContents = '';
        $contentSections = '';

        // Generate table of contents
        foreach ($sections as $section) {
            $tableOfContents .= sprintf(
                '<li><a href="#%s" class="toc-link">%s</a></li>',
                $section['slug'],
                htmlspecialchars($section['title'])
            );
        }

        // Generate content sections
        foreach ($sections as $section) {
            $contentSections .= sprintf(
                '<section id="%s" class="content-section">
                    <h2 class="section-title">%s</h2>
                    <div class="section-content">%s</div>
                </section>',
                $section['slug'],
                htmlspecialchars($section['title']),
                $section['content']
            );
        }

        // Complete HTML template
        $html = <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$documentTitle}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
            padding: 20px;
            min-height: 100vh;
        }

        .sidebar {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 20px;
            position: sticky;
            top: 20px;
            height: fit-content;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }

        .sidebar h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
            font-size: 1.2em;
        }

        .toc {
            list-style: none;
        }

        .toc li {
            margin-bottom: 8px;
        }

        .toc-link {
            color: #3498db;
            text-decoration: none;
            padding: 8px 12px;
            border-radius: 5px;
            display: block;
            transition: all 0.3s ease;
            font-size: 0.9em;
        }

        .toc-link:hover {
            background-color: #3498db;
            color: white;
            transform: translateX(5px);
        }

        .toc-link.active {
            background-color: #2980b9;
            color: white;
        }

        .main-content {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 30px;
        }

        .document-title {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 30px;
            text-align: center;
            padding-bottom: 20px;
            border-bottom: 3px solid #3498db;
        }

        .content-section {
            margin-bottom: 50px;
            scroll-margin-top: 20px;
        }

        .section-title {
            color: #2c3e50;
            font-size: 1.8em;
            margin-bottom: 20px;
            padding: 15px 0;
            border-left: 4px solid #3498db;
            padding-left: 20px;
            background: linear-gradient(90deg, #ecf0f1 0%, transparent 100%);
        }

        .section-content {
            font-size: 1.1em;
            line-height: 1.8;
            color: #555;
        }

        .section-content p {
            margin-bottom: 15px;
        }

        .section-content h1, .section-content h2, .section-content h3 {
            color: #2c3e50;
            margin: 20px 0 10px 0;
        }

        .section-content ul, .section-content ol {
            margin: 15px 0;
            padding-left: 30px;
        }

        .section-content li {
            margin-bottom: 5px;
        }

        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
            transition: all 0.3s ease;
            opacity: 0;
            visibility: hidden;
        }

        .back-to-top.visible {
            opacity: 1;
            visibility: visible;
        }

        .back-to-top:hover {
            background: #2980b9;
            transform: translateY(-3px);
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                gap: 10px;
                padding: 10px;
            }

            .sidebar {
                position: relative;
                top: auto;
                max-height: none;
            }

            .document-title {
                font-size: 2em;
            }

            .section-title {
                font-size: 1.5em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <aside class="sidebar">
            <h3>📋 Table of Contents</h3>
            <ul class="toc">
                {$tableOfContents}
            </ul>
        </aside>

        <main class="main-content">
            <h1 class="document-title">{$documentTitle}</h1>
            {$contentSections}
        </main>
    </div>

    <button class="back-to-top" id="backToTop" title="Back to top">↑</button>

    <script>
        // Smooth scrolling for TOC links
        document.querySelectorAll('.toc-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);

                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });

                    // Update active link
                    document.querySelectorAll('.toc-link').forEach(l => l.classList.remove('active'));
                    this.classList.add('active');
                }
            });
        });

        // Back to top button
        const backToTopBtn = document.getElementById('backToTop');

        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.add('visible');
            } else {
                backToTopBtn.classList.remove('visible');
            }
        });

        backToTopBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // Highlight current section in TOC
        const sections = document.querySelectorAll('.content-section');
        const tocLinks = document.querySelectorAll('.toc-link');

        function highlightCurrentSection() {
            let current = '';

            sections.forEach(section => {
                const sectionTop = section.offsetTop - 100;
                if (window.pageYOffset >= sectionTop) {
                    current = section.getAttribute('id');
                }
            });

            tocLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        }

        window.addEventListener('scroll', highlightCurrentSection);
        highlightCurrentSection(); // Initial call
    </script>
</body>
</html>
HTML;

        return $html;
    }

    /**
     * Preview generated content
     */
    public function preview($documentId)
    {
        $document = DB::table('dynamic_documents')
            ->where('id', $documentId)
            ->first();

        if (!$document) {
            abort(404, 'Document not found');
        }

        return response($document->generated_html)
            ->header('Content-Type', 'text/html');
    }

    /**
     * List all documents
     */
    public function index()
    {
        $documents = DB::table('dynamic_documents')
            ->select('id', 'title', 'created_at')
            ->orderBy('created_at', 'desc')
            ->get();

        return view('dynamic-content.index', compact('documents'));
    }

    /**
     * Show edit form
     */
    public function edit($id)
    {
        $document = DB::table('dynamic_documents')->where('id', $id)->first();
        $sections = DB::table('dynamic_sections')
            ->where('document_id', $id)
            ->orderBy('order_index')
            ->get();

        if (!$document) {
            abort(404, 'Document not found');
        }

        return view('dynamic-content.edit', compact('document', 'sections'));
    }

    /**
     * Update existing document
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'sections' => 'required|array|min:1',
            'sections.*.title' => 'required|string|max:255',
            'sections.*.content' => 'required|string',
            'document_title' => 'nullable|string|max:255'
        ]);

        try {
            DB::beginTransaction();

            $sections = $request->input('sections', []);
            $documentTitle = $request->input('document_title', 'Dynamic Content Document');

            // Update document
            DB::table('dynamic_documents')
                ->where('id', $id)
                ->update([
                    'title' => $documentTitle,
                    'updated_at' => now()
                ]);

            // Delete existing sections
            DB::table('dynamic_sections')->where('document_id', $id)->delete();

            $sectionData = [];

            // Create new sections
            foreach ($sections as $index => $section) {
                $title = trim($section['title']);
                $content = $section['content'];
                $slug = Str::slug($title) . '-' . ($index + 1);

                DB::table('dynamic_sections')->insert([
                    'document_id' => $id,
                    'title' => $title,
                    'content' => $content,
                    'slug' => $slug,
                    'order_index' => $index,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);

                $sectionData[] = [
                    'title' => $title,
                    'content' => $content,
                    'slug' => $slug,
                    'order' => $index
                ];
            }

            // Regenerate HTML
            $htmlContent = $this->generateScrollableHtml($documentTitle, $sectionData);

            DB::table('dynamic_documents')
                ->where('id', $id)
                ->update([
                    'generated_html' => $htmlContent,
                    'updated_at' => now()
                ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Content updated successfully!',
                'document_id' => $id,
                'preview_url' => route('dynamic-content.preview', $id)
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Error updating content: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete document
     */
    public function destroy($id)
    {
        try {
            DB::beginTransaction();

            // Delete sections first (due to foreign key)
            DB::table('dynamic_sections')->where('document_id', $id)->delete();

            // Delete document
            $deleted = DB::table('dynamic_documents')->where('id', $id)->delete();

            if (!$deleted) {
                throw new \Exception('Document not found');
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Document deleted successfully!'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Error deleting document: ' . $e->getMessage()
            ], 500);
        }
    }
}
