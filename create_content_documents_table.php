<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('content_documents', function (Blueprint $table) {
            $table->id();
            $table->longText('html_content'); // Store generated HTML
            $table->json('sections_data'); // Store original sections data as JSON
            $table->timestamps();
            
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('content_documents');
    }
};
