/* Lead Detail Styles */

/* Business Information Styles */
.business_info_title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 20px;
  color: #333;
}

.business_info_container {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
}

.info_item {
  margin-bottom: 20px;
}

.info_label {
  font-size: 0.9rem;
  color: #6c757d;
  margin-bottom: 5px;
}

.info_value {
  font-size: 1rem;
  font-weight: 500;
  color: #212529;
}
.white_card_header {
  background-color: #0d6efd;
  color: white;
  padding: 15px;
  border-radius: 0;
  margin-bottom: 0;
}

.iris-lead-name {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: white;
}

.box_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-pills {
  border-bottom: none;
  margin: 0;
  padding: 0;
  display: flex;
  width: 100%;
}

.nav-item {
  margin-right: 10px;
}

.nav-link {
  color: white !important;
  font-weight: 500;
  padding: 3px 13px;
  border: none;
  border-radius: 0;
  background-color: transparent;
  position: relative;
  transition: all 0.2s;
  text-align: center;
  font-size: 0.9rem;
}

.nav-link:focus,
.nav-link:hover,
.nav-item.active .nav-link {
  color: #fff !important;
  border: 1px solid;
  padding: 3px 13px;
  border-radius: 50px;
}

.white_card_body {
  padding-top: 20px;
  border-top-left-radius: 0;
  position: relative;
}

/* Left section with horizontal scroll */
.left-section-container {
  max-height: 690px;
  overflow-y: auto;
  padding-right: 15px;
  position: relative;
}

/* Scrollbar styling for left section */
.left-section-container::-webkit-scrollbar {
  width: 8px;
}

.left-section-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.left-section-container::-webkit-scrollbar-thumb {
  background: #1658a5;
  border-radius: 4px;
}

.left-section-container::-webkit-scrollbar-thumb:hover {
  background: #1658a5;
}

.form-label {
  font-weight: 500;
  margin-bottom: 0.25rem;
  color: #495057;
}

.form-control,
.form-select,
input[type="date"] {
  height: 45px;
  padding: 0.375rem 0.75rem;
  border-radius: 4px;
  border: 1px solid #ced4da;
}

/* Fix for date input in different browsers */
input[type="date"]::-webkit-calendar-picker-indicator {
  cursor: pointer;
  opacity: 0.8;
}

.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
  appearance: none;
}

.form-control:disabled,
.form-control[readonly],
.form-select:disabled {
  background-color: #f8f9fa;
  opacity: 1;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #212529;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e9ecef;
}

.input-with-image {
  position: relative;
}

.profile-image-container {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  overflow: hidden;
}

.profile-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.badge {
  font-weight: 500;
  padding: 0.35em 0.65em;
}

.btn-outline-light {
  border-color: rgba(255, 255, 255, 0.5);
  color: white;
}

.btn-outline-light:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

/* Folder link styles */
.folder-link-icon {
  color: #0d6efd;
  cursor: pointer;
  transition: color 0.2s;
}

.folder-link-icon:hover {
  color: #0a58ca;
}

/* Make the folder link labels blue */
label a.ms-2 {
  text-decoration: none;
}

label a.ms-2 svg {
  cursor: pointer;
  transition: transform 0.2s;
}

label a.ms-2:hover svg {
  transform: scale(1.1);
}

/* Notes section styles */
.notes-container {
  padding: 20px;
}

.notes-section {
  margin-top: 10px;
}

.notes-header {
  background-color: #f8f9fa;
  padding: 12px 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.notes-title {
  font-size: 1rem;
  font-weight: 500;
  color: #495057;
  position: relative;
  padding-left: 10px;
}

.notes-title:before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 16px;
  width: 3px;
  background-color: #ff6600;
  border-radius: 2px;
}

.add-note-btn {
  background-color: #ff6600;
  color: white;
  border: none;
  border-radius: 50px;
  padding: 8px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(108, 99, 255, 0.3);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.add-note-btn span {
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.add-note-btn i {
  font-size: 0.9rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.add-note-btn:hover {
  background-color: #e55c00;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(255, 102, 0, 0.4);
}

.add-note-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(108, 99, 255, 0.3);
}

.note-item {
  border-left: 3px solid #1658a5;
  transition: all 0.2s ease;
}

.note-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

.note-text {
  font-size: 0.95rem;
}


.note-meta {
  display: flex;
  align-items: center;
  color: #6c757d;
}

.note-author {
  font-weight: 500;
}

.note-date {
  font-size: 0.9rem;
  color: #495057;
}

.note-time {
  font-size: 0.85rem;
}

.note-content {
  line-height: 1.5;
  color: #212529;
}

.note-content span.d-flex {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  white-space: normal;
  word-break: break-word;
}

.note-content .fw-bold {
  white-space: nowrap;
}

/* Note: All modal styles have been moved to common/Modal.css */

/* Infinite scroll styles */
#scrollableNotesDiv::-webkit-scrollbar {
  width: 8px;
}

#scrollableNotesDiv::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

#scrollableNotesDiv::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

#scrollableNotesDiv::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.text-orange {
  color: #ff6b00;
}

.btn-orange {
  background-color: #ff6b00;
  color: white;
  border: none;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.btn-orange:hover {
  background-color: #e05e00;
  color: white;
}

/* Assigned Users styles */
.assigned-users-list {
  max-height: 300px;
  overflow-y: auto;
}

.simple-user-item {
  transition: all 0.2s ease;
  background-color: #f8f9fa;
  padding: 8px 12px;
  border-radius: 4px;
  position: relative;
  border: 1px solid #e9ecef;
}

.simple-user-item:hover {
  background-color: #f1f3f5;
}

.user-name-simple {
  font-weight: 500;
  font-size: 0.9rem;
  color: #333;
}

.btn-remove-user {
  background: none;
  border: none;
  color: #dc3545;
  font-size: 1.2rem;
  line-height: 1;
  padding: 0 5px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.2s;
}

.btn-remove-user:hover {
  color: #c82333;
  transform: scale(1.2);
}

/* React Select styles */
.react-select-container {
  font-size: 0.9rem;
}

.react-select__control {
  border-color: #ced4da !important;
  box-shadow: none !important;
  min-height: 45px !important;
}

.react-select__control:hover {
  border-color: #adb5bd !important;
}

.react-select__control--is-focused {
  border-color: #86b7fe !important;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25) !important;
}

.react-select__menu {
  z-index: 10 !important;
  border: 1px solid #ced4da !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

.react-select__option {
  padding: 10px 12px !important;
  cursor: pointer !important;
}

.react-select__option--is-selected {
  background-color: #0d6efd !important;
}

.react-select__option--is-focused:not(.react-select__option--is-selected) {
  background-color: #f0f4ff !important;
}

.assign-user-btn {
  background-color: #1e5bb7;
  color: white;
  border: none;
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
  border-radius: 4px;
}

.assign-user-btn:hover:not(:disabled) {
  background-color: #164a96;
  color: white;
}

.assign-user-btn:disabled {
  background-color: #6c757d;
  opacity: 0.65;
}

/* Save and Cancel buttons */

.action-buttons {
  display: flex;
  justify-content: start;
  gap: 15px;
  width: 100%;
}

.save-btn {
  background-color: #ff6600;
  color: white;
  border: none;
  padding: 10px 40px;
  font-weight: 500;
  transition: all 0.3s ease;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(255, 102, 0, 0.3);
  min-width: 120px;
}

.save-btn:hover {
  background-color: #e55c00;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(255, 102, 0, 0.4);
}

.save-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(255, 102, 0, 0.3);
}

.cancel-btn {
  background-color: #ff0000;
  color: white;
  border: none;
  padding: 10px 40px;
  font-weight: 500;
  transition: all 0.3s ease;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(255, 0, 0, 0.3);
  min-width: 120px;
}

.cancel-btn:hover {
  background-color: #e50000;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(255, 0, 0, 0.4);
}

.cancel-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(255, 0, 0, 0.3);
}

/* Affiliate Commission Styles */
.affiliate-commission-container {
  border-top: 1px solid #e9ecef;
  margin-top: 20px;
  padding-top: 20px;
}

.tier-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #212529;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e9ecef;
}

.commission-input {
  height: 45px;
  padding: 0.375rem 0.75rem;
  border-radius: 4px;
  border: 1px solid #ced4da;
}

.commission-input:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.commission-select {
  width: 100%;
}

/* Contacts Tab Styles */
.custom_opp_create_btn {
  display: flex;
  gap: 15px;
}

.custom_opp_create_btn a {
  background: #ff5c00 !important;
  width: auto;
  padding: 8px 20px;
  font-weight: 600;
  color: #fff;
  line-height: 1.5;
  border-radius: .25rem;
  transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}

.contact_tab_data .card-exam {
  padding: 10px 10px 10px 10px;
  height: 100%;
}

.custom_opp_tab_header {
  border-bottom: 1px solid #d5d5d5;
  padding-bottom: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.custom_opp_tab_header h5 {
  font-weight: 600;
  font-size: 15px;
  /* color: #0f67ae; */
}


.opp_edit_dlt_btn .edit_contact {
  background: #ff5c00 !important;
  border: 0 !important;
  padding: 5px;
  font-weight: 600;
  font-size: 12px;
  color: #fff !important;
  border-radius: 5px;
  width: 25px;
  height: 25px;
  display: inline-block;
  text-align: center;
}

.opp_edit_dlt_btn .delete_contact {
  background: #ff5c00 !important;
  border: 0 !important;
  padding: 5px !important;
  font-weight: 600;
  font-size: 12px;
  color: #fff !important;
  border-radius: 5px !important;
  margin-left: 5px;
  width: 25px;
  height: 25px;
  display: inline-block;
  text-align: center;
}

.contact_tab_data .circle {
  width: 60px;
  height: 60px;
  line-height: 60px;
  border-radius: 50% !important;
  -moz-border-radius: 50% !important;
  -webkit-border-radius: 50% !important;
  text-align: center;
  color: #0f67ae;
  font-size: 20px;
  text-transform: uppercase;
  font-weight: 700;
  background: #f3f0f2;
  border: 1px solid rgb(231 231 231 / 70%);
  top: 0;
  margin-right: 10px;
}

.contact_tab_data .card-exam-title {
  text-align: left;
  width: 80%;
}

.contact_tab_data .card-exam-title p:first-child {
  color: #000;
  line-height: 18px;
}

.card-exam-title p a {
  color: #0f67ae;
}

.contact_tab_data .card-exam-title p {
  font-size: 13px;
  color: #000000;
  font-weight: 500;
  margin-bottom: 7px;
  word-break: break-all;
}

.custom_opp_tab {
  padding: 5px 0px 12px 0px;
  border: 1px solid #d8d8d882;
  margin-top: 18px !important;
  box-shadow: 5px 5px 10px 0px rgb(178 178 178 / 40%) !important;
  margin-left: 0;
  margin-right: 0;
  margin-bottom: 20px;
}

.custom_opp_tab .custom_opp_tab_header {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #d5d5d5;
  padding-bottom: 5px;
  align-items: baseline;
}

.custom_opp_tab .custom_opp_tab_header h5 {
  font-weight: 600;
  font-size: 15px;
  width: 90%;
}

.opp_edit_dlt_btn.projects-iris {
  width: auto !important;
}

.opp_edit_dlt_btn .edit_project {
  background: #ff5c00 !important;
  border: 0 !important;
  padding: 5px;
  font-weight: 600;
  font-size: 12px;
  color: #fff !important;
  border-radius: 5px;
  width: 25px;
  height: 25px;
  display: inline-block;
  text-align: center;
  margin-right: 5px;
}

.opp_edit_dlt_btn .delete_project {
  background: #ff5c00 !important;
  border: 0 !important;
  padding: 5px;
  font-weight: 600;
  font-size: 12px;
  color: #fff !important;
  border-radius: 5px;
  width: 25px;
  height: 25px;
  display: inline-block;
  text-align: center;
}

.custom_opp_tab .lead_des {
  margin-top: 10px;
}

.custom_opp_tab .lead_des p {
  color: #000;
  font-weight: 400;
  margin-bottom: 5px;
  font-size: 14px;
}

.custom_opp_tab .lead_des p b {
  font-weight: 700;
}

/* Account Info Section Styles */
.section-subtitle {
  font-size: 15px;
  margin-bottom: 20px;
  font-weight: 700;
  z-index: 1;
  overflow: hidden;
  position: relative;
}

.sub-section-title {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  margin-top: 15px;
  margin-bottom: 15px;
  border-bottom: 1px dashed #dee2e6;
  padding-bottom: 8px;
}



.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.card-header h5 {
  margin-bottom: 0;
  font-weight: 600;
  color: #212529;
}

/* Style for read-only inputs */
input[readonly],
select[disabled] {
  background-color: #f8f9fa;
  cursor: not-allowed;
  opacity: 0.8;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .lead-detail-tabs .nav-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
  }

  .section-title {
    font-size: 1rem;
  }

  .tier-title {
    font-size: 1rem;
  }

  .section-subtitle {
    font-size: 0.9rem;
  }
}

.custom-checkbox {
  margin-top: 30px;
  display: flex;
  align-items: center;
}

.custom-checkbox label {
  margin-bottom: 0;
}

.custom_opp_tab {
  padding: 0px 0px 12px 0px;
  border: 1px solid #d8d8d882;
  box-shadow: 5px 5px 10px 0px rgb(178 178 178 / 40%) !important;
  margin-left: 0;
  margin-right: 0;
  margin-bottom: 20px;
  border-bottom: 2px solid #ff5c00;
}

.custom_opp_tab .custom_opp_tab_header {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #d5d5d5;
  padding-bottom: 10px;
  padding-top: 10px;
  align-items: baseline;
}

.custom_opp_tab .custom_opp_tab_header h5 {
  font-weight: 600;
  font-size: 15px;
  width: 90%;
}

.badge.bg-cancel {
  background: #777 !important;
  color: #fff;
  font-size: 12px;
  text-transform: uppercase;
}

.custom_opp_tab .lead_des {
  margin-top: 10px;
}

.custom_opp_tab .lead_des p {
  color: #000;
  font-weight: 400;
  margin-bottom: 5px;
  margin-top: 8px;
}


/* payment history section css  start*/

.total-payment-invoice{
    border: 1px solid #b1c9db;
    margin-bottom: 7px !important;
    border-radius: 5px;
    background: rgba(220, 233, 244, 0.50);
    display: flex;
    padding: 12px 10px;
    align-items: center;
    justify-content: space-between;
}

.total-payment-invoice h4 {
  margin-bottom: 0;
  color: #333333;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.total-payment-invoice p {
  margin-bottom: 0;
  color: #333333;
  font-size: 14px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  margin-right: 82px;
  margin-top: 0px;
}

.expand_pp_div{
    margin-top: 7px;
    font-weight: 600;
}
/* payment history section css  end*/
