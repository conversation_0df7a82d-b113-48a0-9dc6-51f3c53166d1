<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Form with Text Editor</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- CKEditor 5 -->
    <script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
    <style>
        .form-section {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            background-color: #f8f9fa;
            position: relative;
        }
        .remove-btn {
            position: absolute;
            top: 10px;
            right: 10px;
        }
        .add-section-btn {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .add-section-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }
        .section-title {
            color: #495057;
            font-weight: 600;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">Dynamic Form with Text Editors</h4>
                    </div>
                    <div class="card-body">
                        <form id="dynamicForm" method="POST" action="{{ route('your.route') }}">
                            @csrf
                            
                            <!-- Container for dynamic sections -->
                            <div id="sectionsContainer">
                                <!-- Initial section -->
                                <div class="form-section" data-section="0">
                                    <h5 class="section-title">Section 1</h5>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="title_0" class="form-label">Title</label>
                                                <input type="text" 
                                                       class="form-control" 
                                                       id="title_0" 
                                                       name="sections[0][title]" 
                                                       placeholder="Enter title"
                                                       required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="category_0" class="form-label">Category</label>
                                                <input type="text" 
                                                       class="form-control" 
                                                       id="category_0" 
                                                       name="sections[0][category]" 
                                                       placeholder="Enter category">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="content_0" class="form-label">Content</label>
                                        <textarea class="form-control text-editor" 
                                                  id="content_0" 
                                                  name="sections[0][content]" 
                                                  rows="6"
                                                  placeholder="Enter your content here..."></textarea>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Add Section Button -->
                            <div class="text-center mb-4">
                                <button type="button" 
                                        class="btn add-section-btn" 
                                        id="addSectionBtn">
                                    <i class="fas fa-plus me-2"></i>Add Another Section
                                </button>
                            </div>
                            
                            <!-- Submit Button -->
                            <div class="text-center">
                                <button type="submit" class="btn btn-success btn-lg px-5">
                                    <i class="fas fa-save me-2"></i>Save All Sections
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js"></script>
    
    <script>
        let sectionCount = 0;
        let editors = {};

        // Initialize CKEditor for existing textareas
        document.addEventListener('DOMContentLoaded', function() {
            initializeEditor(0);
        });

        // Function to initialize CKEditor
        function initializeEditor(sectionIndex) {
            ClassicEditor
                .create(document.querySelector(`#content_${sectionIndex}`), {
                    toolbar: [
                        'heading', '|',
                        'bold', 'italic', 'underline', '|',
                        'bulletedList', 'numberedList', '|',
                        'outdent', 'indent', '|',
                        'blockQuote', 'insertTable', '|',
                        'undo', 'redo'
                    ],
                    placeholder: 'Enter your content here...'
                })
                .then(editor => {
                    editors[sectionIndex] = editor;
                })
                .catch(error => {
                    console.error('Error initializing editor:', error);
                });
        }

        // Add new section
        document.getElementById('addSectionBtn').addEventListener('click', function() {
            sectionCount++;
            const container = document.getElementById('sectionsContainer');
            
            const newSection = document.createElement('div');
            newSection.className = 'form-section';
            newSection.setAttribute('data-section', sectionCount);
            
            newSection.innerHTML = `
                <button type="button" class="btn btn-danger btn-sm remove-btn" onclick="removeSection(${sectionCount})">
                    <i class="fas fa-times"></i>
                </button>
                <h5 class="section-title">Section ${sectionCount + 1}</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="title_${sectionCount}" class="form-label">Title</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="title_${sectionCount}" 
                                   name="sections[${sectionCount}][title]" 
                                   placeholder="Enter title"
                                   required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="category_${sectionCount}" class="form-label">Category</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="category_${sectionCount}" 
                                   name="sections[${sectionCount}][category]" 
                                   placeholder="Enter category">
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="content_${sectionCount}" class="form-label">Content</label>
                    <textarea class="form-control text-editor" 
                              id="content_${sectionCount}" 
                              name="sections[${sectionCount}][content]" 
                              rows="6"
                              placeholder="Enter your content here..."></textarea>
                </div>
            `;
            
            container.appendChild(newSection);
            
            // Initialize CKEditor for the new textarea
            setTimeout(() => {
                initializeEditor(sectionCount);
            }, 100);
            
            // Smooth scroll to new section
            newSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
        });

        // Remove section
        function removeSection(sectionIndex) {
            // Destroy the editor instance
            if (editors[sectionIndex]) {
                editors[sectionIndex].destroy();
                delete editors[sectionIndex];
            }
            
            // Remove the section element
            const section = document.querySelector(`[data-section="${sectionIndex}"]`);
            if (section) {
                section.remove();
            }
            
            // Update section titles
            updateSectionTitles();
        }

        // Update section titles after removal
        function updateSectionTitles() {
            const sections = document.querySelectorAll('.form-section');
            sections.forEach((section, index) => {
                const title = section.querySelector('.section-title');
                if (title) {
                    title.textContent = `Section ${index + 1}`;
                }
            });
        }

        // Form submission handler
        document.getElementById('dynamicForm').addEventListener('submit', function(e) {
            // Update editor content to textareas before submission
            Object.keys(editors).forEach(key => {
                if (editors[key]) {
                    const textarea = document.querySelector(`#content_${key}`);
                    if (textarea) {
                        textarea.value = editors[key].getData();
                    }
                }
            });
        });
    </script>
</body>
</html>
