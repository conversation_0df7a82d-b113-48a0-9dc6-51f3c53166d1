/* Sidebar Menu Styles */

.metismenu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.metismenu li {
  margin: 0;
  position: relative;
}

.metismenu a {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  color: #eee;
  text-decoration: none;
  transition: background-color 0.3s;
}

.metismenu a:hover {
  background-color: #32373c;
  color: #fff;
}

.wp-menu-arrow {
  display: none;
}

.wp-menu-image {
  width: 20px;
  height: 20px;
  margin-right: 10px;
  opacity: 0.7;
}

.wp-menu-name {
  font-size: 14px;
}

.metismenu .mm-collapse {
  list-style: none;
  padding: 0;
  margin: 0;
  background-color: #32373c;
  display: none;
}

.metismenu .mm-collapse.mm-show {
  display: block !important;
}

.wp-submenu a {
  padding-left: 35px;
  font-size: 13px;
}

.wp-submenu-head {
  display: none;
}

/* Active menu styles */
.mm-active > a {
  background-color: #0073aa;
  color: #fff;
}

.wp-has-current-submenu {
  background-color: #0073aa;
  color: #fff;
}

.current {
  color: #fff;
}

.current a {
  color: #fff;
  font-weight: bold;
}

/* Menu arrow styles */
.menu-arrow {
  position: absolute;
  right: 15px;
  font-size: 24px;
  transition: transform 0.3s;
  color: #a7aaad;
}

.mm-active > a .menu-arrow,
.wp-has-current-submenu .menu-arrow {
  color: #fff;
  transform: rotate(90deg);
}

/* MetisMenu specific styles */
.metismenu .mm-active > ul.mm-collapse {
  display: block !important;
}

/* Additional MetisMenu styles */
.metismenu a {
  position: relative;
}

.metismenu .mm-active > a {
  background-color: #0073aa;
  color: #fff;
}

.metismenu .mm-active > ul.mm-collapse {
  background-color: #32373c;
}

.metismenu .mm-collapse li a {
  padding-left: 35px;
}

/* Position the menu items properly */
.wp-has-submenu > a {
  position: relative;
  padding-right: 30px;
}


.metismenu a.wp-has-submenu::after {
  display: none;
}