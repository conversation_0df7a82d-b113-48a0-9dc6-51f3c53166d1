/* Document Table Styles */
.document-table {
  overflow: hidden;
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 0;
  width: 100%;
  border: none;
}

.document-row {
  transition: all 0.3s ease;
  border-bottom: 1px solid #e9ecef;
}

.document-row:last-child {
  border-bottom: none;
}

.document-row:hover {
  background-color: #f0f7ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.document-row td {
  padding: 20px;
  vertical-align: middle;
}

.document-name {
  font-weight: 600;
  color: #333;
}

.document-icon {
  font-size: 20px;
  margin-right: 12px;
  background-color: #e9f5ff;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.file-link {
  color: #0d6efd;
  text-decoration: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  font-weight: 500;
}

.file-link:hover {
  color: #0a58ca;
  transform: translateY(-2px);
}

.file-icon {
  background-color: #e1f5fe;
  color: #0288d1;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  margin-right: 10px;
  font-size: 16px;
}

.status-badge {
  display: inline-block;
  padding: 8px 16px;
  border-radius: 30px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.status-review {
  background-color: #e3f2fd !important;
  color: #1976d2 !important;
  border: 1px solid #bbdefb !important;
}

.status-pending {
  background-color: #fff8e1 !important;
  color: #ff8f00 !important;
  border: 1px solid #ffe082 !important;
}

.status-approved {
  background-color: #e8f5e9 !important;
  color: #2e7d32 !important;
  border: 1px solid #c8e6c9 !important;
}

.status-rejected {
  background-color: #ffebee !important;
  color: #c62828 !important;
  border: 1px solid #ffcdd2 !important;
}

/* Toggle button styling */
.toggle-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.toggle-option {
  font-weight: 600;
  font-size: 14px;
  transition: color 0.3s ease;
}

/* Style for "No" option */
.toggle-option:first-child {
  color: #dc3545;
}

/* Style for "Yes" option */
.toggle-option:last-child {
  color: #28a745;
}

/* When toggle is checked (Yes), make Yes option more prominent */
input:checked ~ .toggle-option:last-child {
  color: #28a745;
  font-weight: 700;
}

/* When toggle is not checked (No), make No option more prominent */
input:not(:checked) ~ .toggle-option:first-child {
  color: #dc3545;
  font-weight: 700;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 30px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #dc3545; /* Red for "No" */
  transition: .4s;
  border-radius: 30px;
  border: 1px solid #ced4da;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 22px;
  width: 22px;
  left: 4px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

input:checked + .toggle-slider {
  background-color: #28a745; /* Green for "Yes" */
}

input:checked + .toggle-slider:before {
  transform: translateX(29px);
}

/* Disabled toggle styling */
.toggle-switch.disabled {
  opacity: 0.8;
  cursor: not-allowed;
}

.toggle-switch.disabled .toggle-slider {
  cursor: not-allowed;
  border-color: #dee2e6;
}

/* Maintain red color for "No" when disabled */
.toggle-switch.disabled input:not(:checked) + .toggle-slider {
  background-color: rgba(220, 53, 69, 0.7); /* Slightly faded red */
}

/* Maintain green color for "Yes" when disabled */
.toggle-switch.disabled input:checked + .toggle-slider {
  background-color: rgba(40, 167, 69, 0.7); /* Slightly faded green */
}

.toggle-option.disabled {
  color: #6c757d;
  font-weight: 600;
}

/* Upload button styling */
.upload-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  background-color: #fff;
  color: #28a745;
  border: 2px solid #28a745;
  border-radius: 30px;
  font-weight: 600;
  font-size: 13px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.upload-btn:hover {
  background-color: #28a745;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.2);
}

.upload-btn i {
  margin-right: 8px;
  font-size: 14px;
}

/* Comment button styling */
.comment-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  background-color: #fff;
  color: #0d6efd;
  border: 2px solid #0d6efd;
  border-radius: 30px;
  font-weight: 600;
  font-size: 13px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.comment-btn:hover {
  background-color: #0d6efd;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(13, 110, 253, 0.2);
}

.comment-btn i {
  margin-right: 8px;
  font-size: 14px;
}

/* Add document button */
.add-document-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 2rem auto;
  padding: 12px 24px;
  background: linear-gradient(135deg, #0062cc, #0d6efd);
  color: white;
  border: none;
  border-radius: 30px;
  font-weight: 600;
  font-size: 14px;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(13, 110, 253, 0.3);
}

.add-document-btn:hover {
  background: linear-gradient(135deg, #0d6efd, #0062cc);
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(13, 110, 253, 0.4);
}

.add-document-btn i {
  margin-right: 10px;
  font-size: 16px;
}
