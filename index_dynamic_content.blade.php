@extends('layouts.app')

@section('title', 'Dynamic Content Documents')

@section('content')
<div class="container my-5">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-lg">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h3 class="mb-0"><i class="fas fa-file-alt me-2"></i>Dynamic Content Documents</h3>
                    <a href="{{ route('dynamic-content.create') }}" class="btn btn-light">
                        <i class="fas fa-plus me-2"></i>Create New Document
                    </a>
                </div>
                <div class="card-body">
                    @if($documents->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>Title</th>
                                        <th>Created Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($documents as $document)
                                    <tr>
                                        <td>{{ $document->id }}</td>
                                        <td>
                                            <strong>{{ $document->title }}</strong>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                {{ \Carbon\Carbon::parse($document->created_at)->format('M d, Y h:i A') }}
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('dynamic-content.preview', $document->id) }}" 
                                                   class="btn btn-sm btn-success" 
                                                   target="_blank"
                                                   title="View Generated Content">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('dynamic-content.edit', $document->id) }}" 
                                                   class="btn btn-sm btn-warning"
                                                   title="Edit Document">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" 
                                                        class="btn btn-sm btn-danger" 
                                                        onclick="deleteDocument({{ $document->id }})"
                                                        title="Delete Document">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-file-alt fa-5x text-muted mb-3"></i>
                            <h4 class="text-muted">No documents found</h4>
                            <p class="text-muted">Create your first dynamic content document to get started.</p>
                            <a href="{{ route('dynamic-content.create') }}" class="btn btn-primary btn-lg">
                                <i class="fas fa-plus me-2"></i>Create First Document
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title"><i class="fas fa-exclamation-triangle me-2"></i>Confirm Delete</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this document? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-2"></i>Delete Document
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    let documentToDelete = null;

    function deleteDocument(documentId) {
        documentToDelete = documentId;
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }

    document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
        if (documentToDelete) {
            fetch(`{{ url('dynamic-content/delete') }}/${documentToDelete}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                alert('Error deleting document: ' + error.message);
            });
        }
    });
</script>
@endsection
