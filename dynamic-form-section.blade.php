<!-- Dynamic Form Section - Add this to your existing create.blade.php -->

<!-- CSS for Dynamic Form (add to your head section) -->
<style>
    .dynamic-section {
        border: 1px solid #e3e6f0;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        background-color: #f8f9fa;
        position: relative;
    }
    .remove-section-btn {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 10;
    }
    .add-more-btn {
        background: linear-gradient(45deg, #4e73df, #224abe);
        border: none;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    .add-more-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(78, 115, 223, 0.3);
    }
</style>

<!-- Dynamic Form HTML (add this where you want the form) -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Dynamic Content Sections</h6>
    </div>
    <div class="card-body">
        <!-- Container for dynamic sections -->
        <div id="dynamicSectionsContainer">
            <!-- Initial section -->
            <div class="dynamic-section" data-section-id="0">
                <h6 class="text-gray-800 mb-3">Section 1</h6>
                
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group mb-3">
                            <label for="section_title_0" class="form-label">Title</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="section_title_0" 
                                   name="dynamic_sections[0][title]" 
                                   placeholder="Enter section title"
                                   required>
                        </div>
                    </div>
                </div>
                
                <div class="form-group mb-3">
                    <label for="section_content_0" class="form-label">Content</label>
                    <textarea class="form-control dynamic-editor" 
                              id="section_content_0" 
                              name="dynamic_sections[0][content]" 
                              rows="5"
                              placeholder="Enter your content here..."></textarea>
                </div>
            </div>
        </div>
        
        <!-- Add More Button -->
        <div class="text-center">
            <button type="button" class="btn add-more-btn" id="addMoreSectionBtn">
                <i class="fas fa-plus me-2"></i>Add Another Section
            </button>
        </div>
    </div>
</div>

<!-- JavaScript for Dynamic Form (add before closing body tag) -->
<script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
<script>
    let dynamicSectionCount = 0;
    let dynamicEditors = {};

    // Initialize first editor
    document.addEventListener('DOMContentLoaded', function() {
        initializeDynamicEditor(0);
    });

    // Function to initialize CKEditor
    function initializeDynamicEditor(sectionId) {
        ClassicEditor
            .create(document.querySelector(`#section_content_${sectionId}`), {
                toolbar: [
                    'heading', '|',
                    'bold', 'italic', 'underline', '|',
                    'bulletedList', 'numberedList', '|',
                    'blockQuote', 'insertTable', '|',
                    'undo', 'redo'
                ],
                placeholder: 'Enter your content here...',
                height: '200px'
            })
            .then(editor => {
                dynamicEditors[sectionId] = editor;
            })
            .catch(error => {
                console.error('Error initializing editor:', error);
            });
    }

    // Add new section
    document.getElementById('addMoreSectionBtn').addEventListener('click', function() {
        dynamicSectionCount++;
        const container = document.getElementById('dynamicSectionsContainer');
        
        const newSection = document.createElement('div');
        newSection.className = 'dynamic-section';
        newSection.setAttribute('data-section-id', dynamicSectionCount);
        
        newSection.innerHTML = `
            <button type="button" class="btn btn-danger btn-sm remove-section-btn" onclick="removeDynamicSection(${dynamicSectionCount})">
                <i class="fas fa-times"></i>
            </button>
            <h6 class="text-gray-800 mb-3">Section ${dynamicSectionCount + 1}</h6>
            
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group mb-3">
                        <label for="section_title_${dynamicSectionCount}" class="form-label">Title</label>
                        <input type="text" 
                               class="form-control" 
                               id="section_title_${dynamicSectionCount}" 
                               name="dynamic_sections[${dynamicSectionCount}][title]" 
                               placeholder="Enter section title"
                               required>
                    </div>
                </div>
            </div>
            
            <div class="form-group mb-3">
                <label for="section_content_${dynamicSectionCount}" class="form-label">Content</label>
                <textarea class="form-control dynamic-editor" 
                          id="section_content_${dynamicSectionCount}" 
                          name="dynamic_sections[${dynamicSectionCount}][content]" 
                          rows="5"
                          placeholder="Enter your content here..."></textarea>
            </div>
        `;
        
        container.appendChild(newSection);
        
        // Initialize CKEditor for the new textarea
        setTimeout(() => {
            initializeDynamicEditor(dynamicSectionCount);
        }, 100);
        
        // Smooth scroll to new section
        newSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
    });

    // Remove section function
    function removeDynamicSection(sectionId) {
        // Destroy the editor instance
        if (dynamicEditors[sectionId]) {
            dynamicEditors[sectionId].destroy();
            delete dynamicEditors[sectionId];
        }
        
        // Remove the section element
        const section = document.querySelector(`[data-section-id="${sectionId}"]`);
        if (section) {
            section.remove();
        }
        
        // Update section titles
        updateDynamicSectionTitles();
    }

    // Update section titles after removal
    function updateDynamicSectionTitles() {
        const sections = document.querySelectorAll('.dynamic-section');
        sections.forEach((section, index) => {
            const title = section.querySelector('h6');
            if (title) {
                title.textContent = `Section ${index + 1}`;
            }
        });
    }

    // Before form submission, update editor content
    document.addEventListener('submit', function(e) {
        // Update all editor content to textareas before submission
        Object.keys(dynamicEditors).forEach(key => {
            if (dynamicEditors[key]) {
                const textarea = document.querySelector(`#section_content_${key}`);
                if (textarea) {
                    textarea.value = dynamicEditors[key].getData();
                }
            }
        });
    });
</script>

<!-- PHP Backend Handler Example -->
{{-- 
// In your controller, handle the dynamic sections like this:

public function store(Request $request)
{
    $dynamicSections = $request->input('dynamic_sections', []);
    
    foreach ($dynamicSections as $index => $section) {
        // Process each section
        $title = $section['title'];
        $content = $section['content'];
        
        // Save to database or process as needed
        // Example:
        // YourModel::create([
        //     'title' => $title,
        //     'content' => $content,
        //     'order' => $index
        // ]);
    }
    
    return redirect()->back()->with('success', 'Sections saved successfully!');
}
--}}
