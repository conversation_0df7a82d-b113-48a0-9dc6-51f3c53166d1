<?php

// Add these routes to your web.php file

use App\Http\Controllers\DynamicContentController;

Route::prefix('dynamic-content')->name('dynamic-content.')->group(function () {
    // Show form to create dynamic content
    Route::get('/', [DynamicContentController::class, 'index'])->name('index');
    
    // Show create form
    Route::get('/create', function () {
        return view('dynamic-content.create');
    })->name('create');
    
    // Store dynamic content
    Route::post('/store', [DynamicContentController::class, 'store'])->name('store');
    
    // Preview generated content
    Route::get('/preview/{id}', [DynamicContentController::class, 'preview'])->name('preview');
    
    // Edit existing document
    Route::get('/edit/{id}', [DynamicContentController::class, 'edit'])->name('edit');
    
    // Update existing document
    Route::put('/update/{id}', [DynamicContentController::class, 'update'])->name('update');
    
    // Delete document
    Route::delete('/delete/{id}', [DynamicContentController::class, 'destroy'])->name('destroy');
});
