<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Content Creator</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        .section-box {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            background: #f8f9fa;
            position: relative;
        }
        
        .remove-btn {
            position: absolute;
            top: 10px;
            right: 10px;
        }
        
        .section-header {
            color: #495057;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .add-btn {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            font-weight: 500;
        }
        
        .add-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="container my-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white text-center">
                        <h3 class="mb-0"><i class="fas fa-edit me-2"></i>Simple Content Creator</h3>
                    </div>
                    <div class="card-body">
                        <form id="contentForm">
                            <!-- Dynamic Sections Container -->
                            <div id="sectionsContainer">
                                <!-- Initial Section -->
                                <div class="section-box" data-index="0">
                                    <h5 class="section-header">Section 1</h5>
                                    
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Title</label>
                                        <input type="text" 
                                               class="form-control" 
                                               name="sections[0][title]" 
                                               placeholder="Enter section title..."
                                               required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Content</label>
                                        <textarea class="form-control" 
                                                  name="sections[0][content]" 
                                                  rows="6"
                                                  placeholder="Enter your content here..."
                                                  required></textarea>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Add More Button -->
                            <div class="text-center mb-4">
                                <button type="button" class="btn add-btn" id="addSectionBtn">
                                    <i class="fas fa-plus me-2"></i>Add Another Section
                                </button>
                            </div>
                            
                            <!-- Submit Button -->
                            <div class="text-center">
                                <button type="submit" class="btn btn-success btn-lg px-5">
                                    <i class="fas fa-save me-2"></i>Generate & Save
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Result Modal -->
    <div class="modal fade" id="resultModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title"><i class="fas fa-check-circle me-2"></i>Content Generated!</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p class="mb-3">Your content has been saved successfully!</p>
                    <div class="mb-3">
                        <label class="form-label fw-bold">Generated HTML:</label>
                        <textarea id="generatedHtml" class="form-control" rows="10" readonly></textarea>
                    </div>
                    <div class="text-center">
                        <button type="button" class="btn btn-primary" onclick="copyToClipboard()">
                            <i class="fas fa-copy me-2"></i>Copy HTML
                        </button>
                        <button type="button" class="btn btn-info" onclick="previewHtml()">
                            <i class="fas fa-eye me-2"></i>Preview
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let sectionIndex = 0;

        // Add new section
        document.getElementById('addSectionBtn').addEventListener('click', function() {
            sectionIndex++;
            const container = document.getElementById('sectionsContainer');
            
            const newSection = document.createElement('div');
            newSection.className = 'section-box';
            newSection.setAttribute('data-index', sectionIndex);
            
            newSection.innerHTML = `
                <button type="button" class="btn btn-danger btn-sm remove-btn" onclick="removeSection(this)">
                    <i class="fas fa-times"></i>
                </button>
                <h5 class="section-header">Section ${sectionIndex + 1}</h5>
                
                <div class="mb-3">
                    <label class="form-label fw-bold">Title</label>
                    <input type="text" 
                           class="form-control" 
                           name="sections[${sectionIndex}][title]" 
                           placeholder="Enter section title..."
                           required>
                </div>
                
                <div class="mb-3">
                    <label class="form-label fw-bold">Content</label>
                    <textarea class="form-control" 
                              name="sections[${sectionIndex}][content]" 
                              rows="6"
                              placeholder="Enter your content here..."
                              required></textarea>
                </div>
            `;
            
            container.appendChild(newSection);
            
            // Smooth scroll to new section
            newSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
            
            // Focus on title input
            setTimeout(() => {
                newSection.querySelector('input').focus();
            }, 300);
        });

        // Remove section
        function removeSection(button) {
            const section = button.closest('.section-box');
            section.style.transition = 'all 0.3s ease';
            section.style.opacity = '0';
            section.style.transform = 'scale(0.95)';
            
            setTimeout(() => {
                section.remove();
                updateSectionNumbers();
            }, 300);
        }

        // Update section numbers
        function updateSectionNumbers() {
            const sections = document.querySelectorAll('.section-box');
            sections.forEach((section, index) => {
                const header = section.querySelector('.section-header');
                if (header) {
                    header.textContent = `Section ${index + 1}`;
                }
            });
        }

        // Form submission
        document.getElementById('contentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Collect form data
            const formData = new FormData(this);
            const sections = [];
            
            // Process sections data
            for (let [key, value] of formData.entries()) {
                const match = key.match(/sections\[(\d+)\]\[(\w+)\]/);
                if (match) {
                    const index = parseInt(match[1]);
                    const field = match[2];
                    
                    if (!sections[index]) {
                        sections[index] = {};
                    }
                    sections[index][field] = value;
                }
            }
            
            // Send to server (replace with your actual endpoint)
            fetch('/api/simple-content/store', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content || ''
                },
                body: JSON.stringify({ sections: sections })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('generatedHtml').value = data.html_content;
                    new bootstrap.Modal(document.getElementById('resultModal')).show();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error saving content');
            });
        });

        // Copy to clipboard
        function copyToClipboard() {
            const textarea = document.getElementById('generatedHtml');
            textarea.select();
            document.execCommand('copy');
            alert('HTML copied to clipboard!');
        }

        // Preview HTML
        function previewHtml() {
            const html = document.getElementById('generatedHtml').value;
            const previewWindow = window.open('', '_blank');
            previewWindow.document.write(html);
            previewWindow.document.close();
        }

        // Auto-resize textareas
        document.addEventListener('input', function(e) {
            if (e.target.tagName === 'TEXTAREA') {
                e.target.style.height = 'auto';
                e.target.style.height = e.target.scrollHeight + 'px';
            }
        });
    </script>
</body>
</html>
