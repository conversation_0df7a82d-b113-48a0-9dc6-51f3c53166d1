<?php

// Example of how to use the SimpleContentController

// 1. In your existing controller or form handler:

use App\Http\Controllers\SimpleContentController;

class YourExistingController extends Controller
{
    public function saveContent(Request $request)
    {
        // Your existing form data
        $sections = [
            [
                'title' => 'Introduction',
                'content' => '<p>This is the introduction section with some content.</p>'
            ],
            [
                'title' => 'Main Content',
                'content' => '<p>This is the main content section.</p><ul><li>Point 1</li><li>Point 2</li></ul>'
            ],
            [
                'title' => 'Conclusion',
                'content' => '<p>This is the conclusion section.</p>'
            ]
        ];

        // Create instance of SimpleContentController
        $contentController = new SimpleContentController();
        
        // Create a fake request with sections data
        $fakeRequest = new Request();
        $fakeRequest->merge(['sections' => $sections]);
        
        // Generate and save HTML
        $result = $contentController->store($fakeRequest);
        $responseData = $result->getData(true);
        
        if ($responseData['success']) {
            // Save the generated HTML to your existing model/table
            $htmlContent = $responseData['html_content'];
            
            // Example: Save to your existing table
            DB::table('your_existing_table')->insert([
                'title' => 'My Document',
                'generated_html' => $htmlContent,
                'created_at' => now()
            ]);
            
            return response()->json([
                'success' => true,
                'html' => $htmlContent
            ]);
        }
        
        return response()->json(['success' => false]);
    }
}

// 2. Direct usage example:

class DirectUsageExample
{
    public function generateSimpleHtml($titleContentPairs)
    {
        // Simple method to generate HTML from title-content pairs
        $tableOfContents = '';
        $contentSections = '';
        
        foreach ($titleContentPairs as $index => $item) {
            $slug = 'section-' . ($index + 1);
            $title = htmlspecialchars($item['title']);
            $content = $item['content'];
            
            $tableOfContents .= "<li><a href=\"#{$slug}\">{$title}</a></li>";
            $contentSections .= "
                <div id=\"{$slug}\" class=\"content-section\">
                    <h2>{$title}</h2>
                    <div>{$content}</div>
                </div>";
        }

        $html = "
        <!DOCTYPE html>
        <html>
        <head>
            <title>Simple Document</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                .container { display: flex; max-width: 1200px; margin: 0 auto; }
                .sidebar { width: 250px; background: #f4f4f4; padding: 20px; position: fixed; height: 100vh; overflow-y: auto; }
                .content { margin-left: 270px; padding: 20px; }
                .content-section { margin-bottom: 40px; scroll-margin-top: 20px; }
                .sidebar ul { list-style: none; padding: 0; }
                .sidebar a { text-decoration: none; color: #333; display: block; padding: 8px 0; }
                .sidebar a:hover { color: #007bff; }
                h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
            </style>
        </head>
        <body>
            <div class=\"container\">
                <div class=\"sidebar\">
                    <h3>Contents</h3>
                    <ul>{$tableOfContents}</ul>
                </div>
                <div class=\"content\">{$contentSections}</div>
            </div>
            <script>
                document.querySelectorAll('.sidebar a').forEach(link => {
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        const target = document.querySelector(this.getAttribute('href'));
                        if (target) target.scrollIntoView({ behavior: 'smooth' });
                    });
                });
            </script>
        </body>
        </html>";
        
        return $html;
    }
}

// 3. Usage in Blade template:

/*
In your Blade file, you can use it like this:

@php
    $sections = [
        ['title' => 'Section 1', 'content' => '<p>Content 1</p>'],
        ['title' => 'Section 2', 'content' => '<p>Content 2</p>'],
    ];
    
    $generator = new DirectUsageExample();
    $html = $generator->generateSimpleHtml($sections);
    
    // Save to database
    DB::table('your_table')->insert([
        'html_content' => $html,
        'created_at' => now()
    ]);
@endphp

<div class="generated-content">
    {!! $html !!}
</div>
*/

// 4. Simple AJAX example for frontend:

/*
// JavaScript to send data and get HTML back
function generateAndSaveContent() {
    const sections = [
        { title: 'Introduction', content: '<p>Intro content</p>' },
        { title: 'Main Section', content: '<p>Main content</p>' }
    ];
    
    fetch('/api/simple-content/store', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({ sections: sections })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // data.html_content contains the generated HTML
            console.log('Generated HTML:', data.html_content);
            
            // You can display it, save it, or use it as needed
            document.getElementById('preview').innerHTML = data.html_content;
        }
    });
}
*/
