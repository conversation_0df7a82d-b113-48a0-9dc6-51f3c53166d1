/* Modal Styles */

/* Modal backdrop */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1055; /* Higher than header (1050) */
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
}

/* Modal container */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1060; /* Higher than header and backdrop */
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  outline: 0;
}

/* Modal dialog */
.modal-dialog {
  position: relative;
  width: auto;
  margin: 1.75rem auto;
  pointer-events: none;
  max-width: 500px;
}

.modal-dialog-centered {
  display: flex;
  align-items: center;
  min-height: calc(100% - 3.5rem);
}

/* Modal sizes */
.modal-sm {
  max-width: 300px;
}

.modal-lg {
  max-width: 800px;
}

.modal-xl {
  max-width: 1140px;
}

/* Modal content */
.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
  outline: 0;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Modal header */
.modal-header {
  border-bottom: 1px solid #dee2e6;
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  background-color: transparent;
}

.modal-title {
    font-size: 17px;
    margin-bottom: 0;
    font-weight: 700;
    color: #1261ab;
}

.modal-header .btn-close {
  font-size: 1rem;
  opacity: 1;
  padding: 0;
  margin: 0;
  position: absolute;
  right: -12px;
  top: 0;
  transform: translateY(-50%);
  background-color: #ff0000;
  color: white;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background-image: none;
  font-weight: 800;
}

.modal-header .btn-close::before {
  content: "×";
  color: white;
  font-size: 21px;
  line-height: 1;
  display: block;
}

.modal-header .btn-close:hover {
  opacity: 0.9;
  background-color: #e60000;
}

/* Modal body */
.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: 1rem;
}

/* Modal footer */
.modal-footer {
  display: flex;
  justify-content: center;
  padding: 1rem;
  border-top: 1px solid #dee2e6;
}

/* Animation */
.modal.fade .modal-dialog {
  transition: transform 0.3s ease-out;
  transform: translate(0, -50px);
}

.modal.show .modal-dialog {
  transform: none;
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .modal-dialog {
    margin: 0.5rem;
    max-width: calc(100% - 1rem);
  }

  .modal-body {
    padding: 0.75rem;
  }

  .modal-footer {
    padding: 0.75rem;
  }
}
