import { useState, useEffect } from 'react';
import { useParams, useLocation } from 'react-router-dom';
import axios from 'axios';
import InfiniteScroll from 'react-infinite-scroll-component';
import Select from 'react-select';
import Swal from 'sweetalert2';
import './common/CommonStyles.css';
import './LeadDetail.css';
import EditContactModal from './EditContactModal';
import AuditLogTab from './AuditLogTab';

const LeadDetail = () => {
  // All your existing state variables and functions remain the same
  // ...

  // This is just a placeholder for the return statement structure
  return (
    <div className="container-fluid">
      <div className="row">
        <div className="col-12">
          <div className="white_card card_height_100 mb_30">
            <div className="white_card_header">
              {/* Header content */}
              <div className="box_header m-0 justify-content-between">
                <h4 className="iris-lead-name">{lead?.lead_id} - {lead?.business_legal_name}</h4>
                <div></div>
              </div>
              
              {/* Tab navigation */}
              <ul className="nav nav-pills" id="pills-tab" role="tablist">
                {/* Tab items */}
                <li className={`nav-item ${activeTab === 'businessInfo' ? 'active' : ''}`}>
                  <a
                    className="nav-link"
                    onClick={(e) => {
                      e.preventDefault();
                      handleTabChange('businessInfo');
                    }}
                    href="#pills-home"
                    role="tab"
                  >
                    Business Info
                  </a>
                </li>
                {/* Other tab items */}
                <li className={`nav-item ${activeTab === 'auditLogs' ? 'active' : ''}`}>
                  <a
                    className="nav-link"
                    onClick={(e) => {
                      e.preventDefault();
                      handleTabChange('auditLogs');
                    }}
                    href="#pills-logs"
                    role="tab"
                  >
                    Audit Logs
                  </a>
                </li>
              </ul>
            </div>

            <div className="white_card_body">
              <div className="row">
                {/* Left Content Area - Changes based on active tab */}
                <div className="col-md-8">
                  {/* Tab content sections */}
                  {activeTab === 'businessInfo' && (
                    <div className="mb-4 left-section-container">
                      {/* Business Info content */}
                    </div>
                  )}
                  
                  {/* Other tab contents */}
                  
                  {/* Audit Logs Tab Content */}
                  {activeTab === 'auditLogs' && (
                    <AuditLogTab leadId={leadId} isActive={activeTab === 'auditLogs'} />
                  )}
                </div>
                
                {/* Right sidebar content */}
                <div className="col-md-4">
                  {/* Sidebar content */}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modals */}
      <EditContactModal
        isOpen={showEditContactModal}
        onClose={handleCloseEditContactModal}
        contactId={currentContactId}
        leadId={leadId}
      />
    </div>
  );
};

export default LeadDetail;
