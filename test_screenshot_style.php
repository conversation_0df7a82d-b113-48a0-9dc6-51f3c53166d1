<?php

// Test script to demonstrate the screenshot-style HTML generation

require_once 'SimpleContentController.php';

// Sample data matching your screenshot
$sections = [
    [
        'title' => 'Introduction',
        'content' => '<p>In the financial services industry, client experience can make or break a company\'s reputation. While front-end representatives often receive the spotlight, it is the backend customer support team that ensures seamless service delivery, accurate transactions, and prompt client communication.</p><p>A strong backend team not only supports growth but also safeguards client trust — an invaluable asset for any financial organization.</p>'
    ],
    [
        'title' => 'Role of Backend Customer Support in Financial Organizations',
        'content' => '<p>Backend customer support in financial organizations serves as the operational backbone that enables front-facing teams to deliver exceptional service. These teams handle complex processes, data management, and system integrations that are invisible to clients but critical for business operations.</p><p>Key responsibilities include:</p><ul><li>Processing and verifying financial transactions</li><li>Managing client data and account information</li><li>Coordinating with various departments for issue resolution</li><li>Ensuring compliance with regulatory requirements</li><li>Maintaining system integrity and security protocols</li></ul>'
    ],
    [
        'title' => 'Beyond the Frontlines: What Backend Support Really Does',
        'content' => '<p>While customers interact primarily with front-end representatives, backend support teams work tirelessly behind the scenes to ensure every interaction is successful. Their work involves complex problem-solving, technical expertise, and deep understanding of financial processes.</p><p>Backend support teams are responsible for:</p><ul><li>Investigating and resolving complex technical issues</li><li>Performing detailed account reconciliations</li><li>Managing escalated cases that require specialized knowledge</li><li>Implementing process improvements and system optimizations</li><li>Training and supporting front-end staff with technical guidance</li></ul>'
    ],
    [
        'title' => 'Why Backend Teams are Critical to Client Trust',
        'content' => '<p>Client trust in financial services is built on reliability, accuracy, and security. Backend teams play a crucial role in maintaining these standards by ensuring that all processes are executed flawlessly and that client information is protected at all times.</p><p>The impact of backend teams on client trust includes:</p><ul><li>Ensuring transaction accuracy and preventing errors</li><li>Maintaining data security and privacy standards</li><li>Providing rapid resolution of complex issues</li><li>Supporting regulatory compliance and audit requirements</li><li>Enabling consistent service quality across all touchpoints</li></ul>'
    ],
    [
        'title' => 'Growth and Competitive Edge Through Strong Backend Operations',
        'content' => '<p>Organizations with robust backend support systems gain significant competitive advantages in the marketplace. These teams enable scalability, improve operational efficiency, and create the foundation for sustainable growth.</p><p>Strategic benefits include:</p><ul><li>Increased operational capacity without proportional cost increases</li><li>Enhanced ability to handle complex financial products and services</li><li>Improved risk management and compliance capabilities</li><li>Greater flexibility to adapt to market changes and regulatory updates</li><li>Stronger foundation for digital transformation initiatives</li></ul>'
    ],
    [
        'title' => 'Key Takeaways',
        'content' => '<p>Backend customer support teams are the unsung heroes of financial organizations, providing the critical infrastructure that enables exceptional client experiences. Their work directly impacts client trust, operational efficiency, and business growth.</p><p>Organizations that invest in strong backend operations will:</p><ul><li>Build stronger client relationships through reliable service delivery</li><li>Achieve greater operational efficiency and cost-effectiveness</li><li>Maintain competitive advantages in an increasingly complex marketplace</li><li>Create sustainable foundations for long-term growth and success</li></ul>'
    ]
];

// Create controller instance
$controller = new SimpleContentController();

// Use reflection to access the private method for testing
$reflection = new ReflectionClass($controller);
$method = $reflection->getMethod('generateSimpleScrollableHtml');
$method->setAccessible(true);

// Generate HTML
$html = $method->invoke($controller, $sections);

// Save to file for testing
file_put_contents('generated_screenshot_style.html', $html);

echo "✅ HTML generated successfully!\n";
echo "📁 File saved as: generated_screenshot_style.html\n";
echo "🌐 Open the file in your browser to see the result\n\n";

// Show how to save to database
echo "💾 To save to database:\n";
echo "DB::table('your_table')->insert([\n";
echo "    'html_content' => \$html,\n";
echo "    'created_at' => now()\n";
echo "]);\n\n";

// Show the first 500 characters of generated HTML
echo "📄 Generated HTML preview:\n";
echo substr($html, 0, 500) . "...\n";

?>

<!-- 
USAGE EXAMPLES:

1. In Laravel Controller:
```php
use App\Http\Controllers\SimpleContentController;

class YourController extends Controller 
{
    public function generateContent(Request $request)
    {
        $sections = $request->input('sections');
        
        $contentController = new SimpleContentController();
        $fakeRequest = new Request(['sections' => $sections]);
        $result = $contentController->store($fakeRequest);
        
        $responseData = $result->getData(true);
        
        if ($responseData['success']) {
            // Save to your table
            DB::table('documents')->insert([
                'title' => 'Blog Document',
                'html_content' => $responseData['html_content'],
                'created_at' => now()
            ]);
            
            return response()->json(['success' => true]);
        }
    }
}
```

2. Direct HTML Generation:
```php
$sections = [
    ['title' => 'Section 1', 'content' => '<p>Content 1</p>'],
    ['title' => 'Section 2', 'content' => '<p>Content 2</p>']
];

$controller = new SimpleContentController();
$reflection = new ReflectionClass($controller);
$method = $reflection->getMethod('generateSimpleScrollableHtml');
$method->setAccessible(true);
$html = $method->invoke($controller, $sections);

// Save to database
DB::table('content')->insert(['html' => $html]);
```

3. AJAX Frontend:
```javascript
const sections = [
    {title: 'Introduction', content: '<p>Intro content</p>'},
    {title: 'Main Content', content: '<p>Main content</p>'}
];

fetch('/api/simple-content/store', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
    },
    body: JSON.stringify({sections: sections})
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('HTML generated:', data.html_content);
        // Use the generated HTML as needed
    }
});
```
-->
