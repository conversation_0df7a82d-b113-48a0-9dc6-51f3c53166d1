<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class SimpleContentController extends Controller
{
    /**
     * Store simple title-content pairs and generate scrollable HTML
     */
    public function store(Request $request)
    {
        // Validate the request
        $request->validate([
            'sections' => 'required|array|min:1',
            'sections.*.title' => 'required|string|max:255',
            'sections.*.content' => 'required|string',
        ]);

        try {
            $sections = $request->input('sections', []);
            
            // Generate simple scrollable HTML
            $htmlContent = $this->generateSimpleScrollableHtml($sections);
            
            // Save to database (assuming you have a 'content_documents' table)
            $documentId = DB::table('content_documents')->insertGetId([
                'html_content' => $htmlContent,
                'sections_data' => json_encode($sections), // Store original data as JSON
                'created_at' => now(),
                'updated_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Content saved successfully!',
                'document_id' => $documentId,
                'html_content' => $htmlContent
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error saving content: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate simple scrollable HTML with title-content pairs
     */
    private function generateSimpleScrollableHtml($sections)
    {
        $tableOfContents = '';
        $contentSections = '';
        
        // Generate table of contents and content sections
        foreach ($sections as $index => $section) {
            $slug = 'section-' . ($index + 1);
            $title = htmlspecialchars($section['title']);
            $content = $section['content'];
            
            // Add to table of contents
            $tableOfContents .= sprintf(
                '<li><a href="#%s" class="toc-link">%s</a></li>',
                $slug,
                $title
            );
            
            // Add content section
            $contentSections .= sprintf(
                '<div id="%s" class="content-section">
                    <h2 class="section-title">%s</h2>
                    <div class="section-content">%s</div>
                </div>',
                $slug,
                $title,
                $content
            );
        }

        // Simple HTML template
        $html = <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Content Document</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f4f4f4;
        }
        
        .container {
            display: flex;
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            padding: 20px;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }
        
        .sidebar h3 {
            margin-bottom: 20px;
            color: #ecf0f1;
            border-bottom: 2px solid #34495e;
            padding-bottom: 10px;
        }
        
        .toc {
            list-style: none;
        }
        
        .toc li {
            margin-bottom: 10px;
        }
        
        .toc-link {
            color: #bdc3c7;
            text-decoration: none;
            display: block;
            padding: 8px 12px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        
        .toc-link:hover {
            background: #34495e;
            color: #ecf0f1;
        }
        
        .main-content {
            margin-left: 250px;
            padding: 40px;
            flex: 1;
        }
        
        .content-section {
            margin-bottom: 40px;
            scroll-margin-top: 20px;
        }
        
        .section-title {
            color: #2c3e50;
            font-size: 1.8em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }
        
        .section-content {
            font-size: 1.1em;
            line-height: 1.8;
            color: #555;
        }
        
        .section-content p {
            margin-bottom: 15px;
        }
        
        .section-content h1, .section-content h2, .section-content h3 {
            color: #2c3e50;
            margin: 20px 0 10px 0;
        }
        
        .section-content ul, .section-content ol {
            margin: 15px 0;
            padding-left: 30px;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                position: relative;
                width: 100%;
                height: auto;
            }
            
            .main-content {
                margin-left: 0;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <h3>Contents</h3>
            <ul class="toc">
                {$tableOfContents}
            </ul>
        </div>
        
        <div class="main-content">
            {$contentSections}
        </div>
    </div>
    
    <script>
        // Simple smooth scrolling
        document.querySelectorAll('.toc-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
HTML;

        return $html;
    }

    /**
     * Get document by ID
     */
    public function show($id)
    {
        $document = DB::table('content_documents')
            ->where('id', $id)
            ->first();
            
        if (!$document) {
            abort(404, 'Document not found');
        }
        
        return response($document->html_content)
            ->header('Content-Type', 'text/html');
    }

    /**
     * Get all documents
     */
    public function index()
    {
        $documents = DB::table('content_documents')
            ->select('id', 'created_at')
            ->orderBy('created_at', 'desc')
            ->get();
            
        return response()->json($documents);
    }
}
