<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class SimpleContentController extends Controller
{
    /**
     * Store simple title-content pairs and generate scrollable HTML
     */
    public function store(Request $request)
    {
        // Validate the request
        $request->validate([
            'sections' => 'required|array|min:1',
            'sections.*.title' => 'required|string|max:255',
            'sections.*.content' => 'required|string',
        ]);

        try {
            $sections = $request->input('sections', []);

            // Generate simple scrollable HTML
            $htmlContent = $this->generateSimpleScrollableHtml($sections);

            // Save to database (assuming you have a 'content_documents' table)
            $documentId = DB::table('content_documents')->insertGetId([
                'html_content' => $htmlContent,
                'sections_data' => json_encode($sections), // Store original data as JSON
                'created_at' => now(),
                'updated_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Content saved successfully!',
                'document_id' => $documentId,
                'html_content' => $htmlContent
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error saving content: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate scrollable HTML matching the screenshot design
     */
    private function generateSimpleScrollableHtml($sections)
    {
        $tableOfContents = '';
        $contentSections = '';

        // Generate table of contents and content sections
        foreach ($sections as $index => $section) {
            $slug = 'section-' . ($index + 1);
            $title = htmlspecialchars($section['title']);
            $content = $section['content'];

            // Add to table of contents with arrow icon
            $tableOfContents .= sprintf(
                '<li><a href="#%s" class="toc-link"><i class="arrow">▷</i> %s</a></li>',
                $slug,
                $title
            );

            // Add content section with numbered title
            $contentSections .= sprintf(
                '<div id="%s" class="content-section">
                    <h2 class="section-title">%d. %s</h2>
                    <div class="section-content">%s</div>
                </div>',
                $slug,
                $index + 1,
                $title,
                $content
            );
        }

        // HTML template matching the screenshot design
        $html = <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blog At A Glance</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: #ff6b35;
            color: white;
            text-align: center;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 30px;
        }

        .content-wrapper {
            padding: 0 40px 40px 40px;
        }

        .toc-section {
            margin-bottom: 40px;
        }

        .toc {
            list-style: none;
            padding: 0;
        }

        .toc li {
            margin-bottom: 12px;
        }

        .toc-link {
            color: #4a90e2;
            text-decoration: none;
            display: flex;
            align-items: center;
            font-size: 16px;
            line-height: 1.4;
            transition: all 0.3s ease;
            padding: 8px 0;
        }

        .toc-link:hover {
            color: #2c5aa0;
            transform: translateX(5px);
        }

        .toc-link .arrow {
            color: #4a90e2;
            margin-right: 8px;
            font-size: 14px;
            transition: transform 0.3s ease;
        }

        .toc-link:hover .arrow {
            transform: rotate(90deg);
        }

        .content-section {
            margin-bottom: 50px;
            scroll-margin-top: 20px;
        }

        .section-title {
            color: #333;
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 25px;
            line-height: 1.3;
        }

        .section-content {
            font-size: 16px;
            line-height: 1.7;
            color: #555;
        }

        .section-content p {
            margin-bottom: 18px;
            text-align: justify;
        }

        .section-content h1, .section-content h2, .section-content h3 {
            color: #333;
            margin: 25px 0 15px 0;
            font-weight: 600;
        }

        .section-content ul, .section-content ol {
            margin: 18px 0;
            padding-left: 25px;
        }

        .section-content li {
            margin-bottom: 8px;
        }

        .section-content strong {
            color: #333;
            font-weight: 600;
        }

        /* Smooth scrolling behavior */
        html {
            scroll-behavior: smooth;
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
            .content-wrapper {
                padding: 0 20px 30px 20px;
            }

            .section-title {
                font-size: 24px;
            }

            .section-content {
                font-size: 15px;
            }

            .toc-link {
                font-size: 15px;
            }
        }

        @media (max-width: 480px) {
            .header {
                font-size: 16px;
                padding: 12px 15px;
            }

            .content-wrapper {
                padding: 0 15px 25px 15px;
            }

            .section-title {
                font-size: 22px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            Blog At A Glance:
        </div>

        <div class="content-wrapper">
            <div class="toc-section">
                <ul class="toc">
                    {$tableOfContents}
                </ul>
            </div>

            <div class="content-sections">
                {$contentSections}
            </div>
        </div>
    </div>

    <script>
        // Enhanced smooth scrolling with offset
        document.querySelectorAll('.toc-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);

                if (targetElement) {
                    const offsetTop = targetElement.offsetTop - 20;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });

                    // Add active state
                    document.querySelectorAll('.toc-link').forEach(l => l.classList.remove('active'));
                    this.classList.add('active');
                }
            });
        });

        // Highlight current section while scrolling
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('.content-section');
            const tocLinks = document.querySelectorAll('.toc-link');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop - 100;
                if (window.pageYOffset >= sectionTop) {
                    current = section.getAttribute('id');
                }
            });

            tocLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
HTML;

        return $html;
    }

    /**
     * Get document by ID
     */
    public function show($id)
    {
        $document = DB::table('content_documents')
            ->where('id', $id)
            ->first();

        if (!$document) {
            abort(404, 'Document not found');
        }

        return response($document->html_content)
            ->header('Content-Type', 'text/html');
    }

    /**
     * Get all documents
     */
    public function index()
    {
        $documents = DB::table('content_documents')
            ->select('id', 'created_at')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json($documents);
    }
}
