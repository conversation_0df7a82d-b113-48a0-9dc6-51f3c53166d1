/* CreateContact styles */ 
.input-group .title-select{
    max-width: 78px;
}

/* .form-control.is-invalid {
  border-color: #dc3545;
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
} */


/* CreateContact styles */ 
.input-group .title-select{
    max-width: 78px;
}

.form-control.is-invalid,
.form-select.is-invalid,
.select2-container--default .select2-selection--single.is-invalid,
.select2-container--default .select2-selection--multiple.is-invalid {
  border-color: #dc3545 !important;
}

.errorMsz {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
  bottom: -20px;
  left: 0;
}

/* Select2 common styles */
.select2-container {
  width: 100% !important;
}

.select2-container--default .select2-selection--single,
.select2-container--default .select2-selection--multiple {
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  min-height: 38px;
}

/* Single Select2 styles */
.select2-container--default .select2-selection--single {
  height: 38px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: 36px;
  padding-left: 12px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 36px;
}

/* Multiple Select2 styles */
.select2-container--default .select2-selection--multiple {
  padding: 0 8px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
  margin-top: 4px;
  margin-right: 5px;
  background-color: #e9ecef;
  border: 1px solid #ced4da;
  border-radius: 3px;
  padding: 2px 8px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  margin-right: 5px;
  color: #6c757d;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #dc3545;
}

.select2-container--default .select2-selection--multiple .select2-selection__rendered {
  padding: 0;
}

.select2-container--default .select2-search--inline .select2-search__field {
  margin-top: 7px;
}

/* Error states */
.select2-container--default .select2-selection--single.is-invalid,
.select2-container--default .select2-selection--multiple.is-invalid {
  border-color: #dc3545 !important;
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Dropdown styles */
.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: #007bff;
}

.select2-container--default .select2-results__option[aria-selected=true] {
  background-color: #e9ecef;
}

/* Clear button for multiple select */
.select2-container--default .select2-selection--multiple .select2-selection__clear {
  margin-right: 5px;
  color: #6c757d;
}

.select2-container--default .select2-selection--multiple .select2-selection__clear:hover {
  color: #dc3545;
}