@extends('layouts.app')

@section('title', 'Create Dynamic Content')

@section('content')
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow-lg">
                <div class="card-header bg-primary text-white text-center">
                    <h3 class="mb-0"><i class="fas fa-edit me-2"></i>Create Dynamic Content Document</h3>
                </div>
                <div class="card-body p-4">
                    <form id="dynamicContentForm" method="POST" action="{{ route('dynamic-content.store') }}">
                        @csrf
                        
                        <!-- Document Title -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">
                                <i class="fas fa-file-alt me-1"></i>Document Title
                            </label>
                            <input type="text" 
                                   class="form-control form-control-lg" 
                                   name="document_title" 
                                   placeholder="Enter document title..."
                                   value="{{ old('document_title', 'My Dynamic Document') }}"
                                   required>
                        </div>
                        
                        <hr class="my-4">
                        
                        <!-- Dynamic Sections Container -->
                        <div id="sectionsContainer">
                            <!-- Initial Section -->
                            <div class="section-container" data-index="0">
                                <div class="section-header">
                                    <h5><i class="fas fa-file-alt me-2"></i>Section 1</h5>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-heading me-1"></i>Section Title
                                    </label>
                                    <input type="text" 
                                           class="form-control" 
                                           name="sections[0][title]" 
                                           placeholder="Enter section title..."
                                           required>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-align-left me-1"></i>Section Content
                                    </label>
                                    <textarea class="form-control content-editor" 
                                              name="sections[0][content]" 
                                              id="content_0"
                                              rows="8"
                                              placeholder="Write your content here..."
                                              required></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Add More Button -->
                        <div class="text-center mb-4">
                            <button type="button" 
                                    class="btn btn-outline-primary btn-lg" 
                                    id="addSectionBtn">
                                <i class="fas fa-plus me-2"></i>Add Another Section
                            </button>
                        </div>
                        
                        <!-- Submit Buttons -->
                        <div class="text-center">
                            <button type="submit" class="btn btn-success btn-lg px-5 me-3">
                                <i class="fas fa-save me-2"></i>Save & Generate
                            </button>
                            <button type="button" class="btn btn-secondary btn-lg px-5" onclick="previewContent()">
                                <i class="fas fa-eye me-2"></i>Preview
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div class="modal fade" id="successModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title"><i class="fas fa-check-circle me-2"></i>Success!</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <p class="mb-3">Your dynamic content has been saved successfully!</p>
                <div class="d-grid gap-2">
                    <a href="#" id="previewLink" class="btn btn-primary" target="_blank">
                        <i class="fas fa-eye me-2"></i>View Generated Content
                    </a>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        Create Another Document
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .section-container {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 25px;
        margin-bottom: 25px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        position: relative;
        transition: all 0.3s ease;
    }
    
    .section-container:hover {
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }
    
    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 2px solid #dee2e6;
    }
    
    .section-header h5 {
        color: #495057;
        font-weight: 700;
        margin: 0;
    }
    
    .remove-btn {
        background: linear-gradient(45deg, #dc3545, #c82333);
        border: none;
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        transition: all 0.3s ease;
    }
    
    .remove-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
    }
    
    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    .content-editor {
        min-height: 150px;
        resize: vertical;
    }
    
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        display: none;
    }
    
    .loading-spinner {
        background: white;
        padding: 30px;
        border-radius: 10px;
        text-align: center;
    }
</style>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-spinner">
        <div class="spinner-border text-primary mb-3" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mb-0">Generating your content...</p>
    </div>
</div>

<script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
<script>
    let sectionIndex = 0;
    let editors = {};

    // Initialize first editor
    document.addEventListener('DOMContentLoaded', function() {
        initializeEditor(0);
    });

    // Initialize CKEditor
    function initializeEditor(index) {
        ClassicEditor
            .create(document.querySelector(`#content_${index}`), {
                toolbar: [
                    'heading', '|',
                    'bold', 'italic', 'underline', '|',
                    'bulletedList', 'numberedList', '|',
                    'blockQuote', 'insertTable', '|',
                    'undo', 'redo'
                ],
                placeholder: 'Write your content here...'
            })
            .then(editor => {
                editors[index] = editor;
            })
            .catch(error => {
                console.error('Error initializing editor:', error);
            });
    }

    // Add new section
    document.getElementById('addSectionBtn').addEventListener('click', function() {
        sectionIndex++;
        const container = document.getElementById('sectionsContainer');
        
        const newSection = document.createElement('div');
        newSection.className = 'section-container';
        newSection.setAttribute('data-index', sectionIndex);
        
        newSection.innerHTML = `
            <div class="section-header">
                <h5><i class="fas fa-file-alt me-2"></i>Section ${sectionIndex + 1}</h5>
                <button type="button" class="btn remove-btn" onclick="removeSection(${sectionIndex})">
                    <i class="fas fa-times me-1"></i>Remove
                </button>
            </div>
            
            <div class="mb-3">
                <label class="form-label fw-bold">
                    <i class="fas fa-heading me-1"></i>Section Title
                </label>
                <input type="text" 
                       class="form-control" 
                       name="sections[${sectionIndex}][title]" 
                       placeholder="Enter section title..."
                       required>
            </div>
            
            <div class="mb-3">
                <label class="form-label fw-bold">
                    <i class="fas fa-align-left me-1"></i>Section Content
                </label>
                <textarea class="form-control content-editor" 
                          name="sections[${sectionIndex}][content]" 
                          id="content_${sectionIndex}"
                          rows="8"
                          placeholder="Write your content here..."
                          required></textarea>
            </div>
        `;
        
        container.appendChild(newSection);
        
        // Initialize editor for new section
        setTimeout(() => {
            initializeEditor(sectionIndex);
        }, 100);
        
        // Smooth scroll to new section
        newSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
    });

    // Remove section
    function removeSection(index) {
        if (editors[index]) {
            editors[index].destroy();
            delete editors[index];
        }
        
        const section = document.querySelector(`[data-index="${index}"]`);
        section.style.transition = 'all 0.3s ease';
        section.style.opacity = '0';
        section.style.transform = 'scale(0.95)';
        
        setTimeout(() => {
            section.remove();
            updateSectionNumbers();
        }, 300);
    }

    // Update section numbers
    function updateSectionNumbers() {
        const sections = document.querySelectorAll('.section-container');
        sections.forEach((section, index) => {
            const header = section.querySelector('.section-header h5');
            if (header) {
                header.innerHTML = `<i class="fas fa-file-alt me-2"></i>Section ${index + 1}`;
            }
        });
    }

    // Form submission
    document.getElementById('dynamicContentForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Show loading overlay
        document.getElementById('loadingOverlay').style.display = 'flex';
        
        // Update editor content to textareas
        Object.keys(editors).forEach(key => {
            if (editors[key]) {
                const textarea = document.querySelector(`#content_${key}`);
                if (textarea) {
                    textarea.value = editors[key].getData();
                }
            }
        });
        
        // Submit form via AJAX
        const formData = new FormData(this);
        
        fetch(this.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            document.getElementById('loadingOverlay').style.display = 'none';
            
            if (data.success) {
                document.getElementById('previewLink').href = data.preview_url;
                new bootstrap.Modal(document.getElementById('successModal')).show();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            document.getElementById('loadingOverlay').style.display = 'none';
            alert('Error saving content: ' + error.message);
        });
    });

    // Preview function
    function previewContent() {
        // Update editor content
        Object.keys(editors).forEach(key => {
            if (editors[key]) {
                const textarea = document.querySelector(`#content_${key}`);
                if (textarea) {
                    textarea.value = editors[key].getData();
                }
            }
        });
        
        // Create preview window
        const previewWindow = window.open('', '_blank');
        previewWindow.document.write('<h1>Preview will be available after saving</h1>');
    }
</script>
@endsection
