<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Dynamic Form</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        .section-container {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            position: relative;
            transition: all 0.3s ease;
        }
        .section-container:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .remove-btn {
            position: absolute;
            top: 15px;
            right: 15px;
        }
        .add-section-btn {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .add-section-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
        }
        .section-header {
            color: #495057;
            font-weight: 700;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #dee2e6;
        }
        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
    </style>
</head>
<body>
    <div class="container my-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card shadow-lg">
                    <div class="card-header bg-primary text-white text-center">
                        <h3 class="mb-0"><i class="fas fa-edit me-2"></i>Dynamic Content Form</h3>
                    </div>
                    <div class="card-body p-4">
                        <form id="mainForm">
                            <!-- Dynamic Sections Container -->
                            <div id="sectionsContainer">
                                <!-- Initial Section -->
                                <div class="section-container" data-index="0">
                                    <h5 class="section-header">
                                        <i class="fas fa-file-alt me-2"></i>Section 1
                                    </h5>
                                    
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">
                                            <i class="fas fa-heading me-1"></i>Title
                                        </label>
                                        <input type="text" 
                                               class="form-control" 
                                               name="sections[0][title]" 
                                               placeholder="Enter your title here..."
                                               required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">
                                            <i class="fas fa-align-left me-1"></i>Content
                                        </label>
                                        <textarea class="form-control" 
                                                  name="sections[0][content]" 
                                                  rows="6"
                                                  placeholder="Write your content here..."
                                                  required></textarea>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Add More Button -->
                            <div class="text-center mb-4">
                                <button type="button" 
                                        class="btn add-section-btn" 
                                        id="addSectionBtn">
                                    <i class="fas fa-plus me-2"></i>Add Another Section
                                </button>
                            </div>
                            
                            <!-- Submit Button -->
                            <div class="text-center">
                                <button type="submit" class="btn btn-success btn-lg px-5">
                                    <i class="fas fa-save me-2"></i>Save All Sections
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let sectionIndex = 0;

        // Add new section
        document.getElementById('addSectionBtn').addEventListener('click', function() {
            sectionIndex++;
            const container = document.getElementById('sectionsContainer');
            
            // Create new section
            const newSection = document.createElement('div');
            newSection.className = 'section-container';
            newSection.setAttribute('data-index', sectionIndex);
            
            newSection.innerHTML = `
                <button type="button" 
                        class="btn btn-danger btn-sm remove-btn" 
                        onclick="removeSection(this)">
                    <i class="fas fa-times"></i>
                </button>
                
                <h5 class="section-header">
                    <i class="fas fa-file-alt me-2"></i>Section ${sectionIndex + 1}
                </h5>
                
                <div class="mb-3">
                    <label class="form-label fw-bold">
                        <i class="fas fa-heading me-1"></i>Title
                    </label>
                    <input type="text" 
                           class="form-control" 
                           name="sections[${sectionIndex}][title]" 
                           placeholder="Enter your title here..."
                           required>
                </div>
                
                <div class="mb-3">
                    <label class="form-label fw-bold">
                        <i class="fas fa-align-left me-1"></i>Content
                    </label>
                    <textarea class="form-control" 
                              name="sections[${sectionIndex}][content]" 
                              rows="6"
                              placeholder="Write your content here..."
                              required></textarea>
                </div>
            `;
            
            container.appendChild(newSection);
            
            // Smooth scroll to new section
            newSection.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'center' 
            });
            
            // Focus on the title input
            setTimeout(() => {
                newSection.querySelector('input[type="text"]').focus();
            }, 500);
        });

        // Remove section
        function removeSection(button) {
            const section = button.closest('.section-container');
            
            // Add fade out animation
            section.style.transition = 'all 0.3s ease';
            section.style.opacity = '0';
            section.style.transform = 'scale(0.95)';
            
            setTimeout(() => {
                section.remove();
                updateSectionNumbers();
            }, 300);
        }

        // Update section numbers after removal
        function updateSectionNumbers() {
            const sections = document.querySelectorAll('.section-container');
            sections.forEach((section, index) => {
                const header = section.querySelector('.section-header');
                if (header) {
                    header.innerHTML = `<i class="fas fa-file-alt me-2"></i>Section ${index + 1}`;
                }
            });
        }

        // Form submission
        document.getElementById('mainForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Collect all form data
            const formData = new FormData(this);
            const sections = [];
            
            // Process sections data
            for (let [key, value] of formData.entries()) {
                const match = key.match(/sections\[(\d+)\]\[(\w+)\]/);
                if (match) {
                    const index = parseInt(match[1]);
                    const field = match[2];
                    
                    if (!sections[index]) {
                        sections[index] = {};
                    }
                    sections[index][field] = value;
                }
            }
            
            // Display collected data (replace with your submission logic)
            console.log('Collected Sections:', sections);
            alert(`Form submitted with ${sections.length} sections!\nCheck console for details.`);
            
            // Here you would typically send the data to your server
            // Example:
            // fetch('/your-endpoint', {
            //     method: 'POST',
            //     headers: {
            //         'Content-Type': 'application/json',
            //         'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            //     },
            //     body: JSON.stringify({ sections: sections })
            // });
        });

        // Auto-resize textareas
        document.addEventListener('input', function(e) {
            if (e.target.tagName === 'TEXTAREA') {
                e.target.style.height = 'auto';
                e.target.style.height = e.target.scrollHeight + 'px';
            }
        });
    </script>
</body>
</html>
