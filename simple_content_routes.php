<?php

// Add these routes to your web.php or api.php file

use App\Http\Controllers\SimpleContentController;

// API Routes for Simple Content
Route::prefix('api/simple-content')->group(function () {
    // Store content and generate HTML
    Route::post('/store', [SimpleContentController::class, 'store']);
    
    // Get all documents
    Route::get('/', [SimpleContentController::class, 'index']);
    
    // Show specific document
    Route::get('/{id}', [SimpleContentController::class, 'show']);
});

// Web Routes (optional)
Route::prefix('simple-content')->group(function () {
    // Show form
    Route::get('/create', function () {
        return view('simple-content.create'); // or return the HTML file
    });
    
    // Show document
    Route::get('/view/{id}', [SimpleContentController::class, 'show']);
});
