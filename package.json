{"name": "login", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "proxy": "node cors-proxy.js", "dev:proxy": "concurrently \"npm run dev\" \"npm run proxy\"", "start": "npm run build && npm run proxy"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fortawesome/fontawesome-free": "^6.7.2", "@hookform/resolvers": "^5.0.1", "@mui/material": "^6.4.2", "axios": "^1.7.9", "body-parser": "^2.2.0", "bootstrap": "^5.3.2", "chart.js": "^4.4.9", "cors": "^2.8.5", "express": "^4.21.2", "http-proxy-middleware": "^2.0.9", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-infinite-scroll-component": "^6.1.0", "react-router-dom": "^7.1.3", "react-select": "^5.10.1", "select2": "^4.1.0-rc.0", "sweetalert2": "^11.19.1", "xlsx": "^0.18.5", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "concurrently": "^8.2.2", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "vite": "^6.0.5"}}