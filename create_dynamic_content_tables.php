<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create dynamic_documents table
        Schema::create('dynamic_documents', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->longText('generated_html')->nullable();
            $table->timestamps();
            
            $table->index(['created_at']);
        });

        // Create dynamic_sections table
        Schema::create('dynamic_sections', function (Blueprint $table) {
            $table->id();
            $table->foreignId('document_id')->constrained('dynamic_documents')->onDelete('cascade');
            $table->string('title');
            $table->longText('content');
            $table->string('slug');
            $table->integer('order_index')->default(0);
            $table->timestamps();
            
            $table->index(['document_id', 'order_index']);
            $table->index(['slug']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dynamic_sections');
        Schema::dropIfExists('dynamic_documents');
    }
};
